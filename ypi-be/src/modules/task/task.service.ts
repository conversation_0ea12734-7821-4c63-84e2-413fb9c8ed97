import { ASSIGN_TASK_STATUS } from '@core/enums/assign-task.enum'
import { FILE_TYPE } from '@core/enums/file.enum'
import { TASK_STATUS } from '@core/enums/task.enum'
import { AssignStaff } from '@modules/assign-task/assign-staff/assign-staff.entity'
import { AssignTask } from '@modules/assign-task/assign-task.entity'
import { AssignStaffDefectDTO } from '@modules/assign-task/dto/assign-task.dto'
import { S3Service } from '@modules/aws/s3.service'
import { Defect } from '@modules/defect/defect.entity'
import { File } from '@modules/file/file.entity'
import { Tank } from '@modules/tank/tank.entity'
import { TaskStaff } from '@modules/task-staff/task-staff.entity'
import {
  CreateNewDefectDTO,
  GetInspectorRemarkTasksDTO,
  GetRectifyTasksDTO,
  GetStaffTasksDTO,
  GetTaskCalendarFormatDTO,
  InspectorAddRemarkDTO,
  InspectorAddRemarkImagesDTO,
  InspectorSubmitDefectDTO,
  RequestRectifyDefectDTO,
  StaffAddRemarkDTO,
  StaffReceiveSignatureDTO,
  UpdateExtraDefectDTO,
  UpdateNewDefectDTO,
} from '@modules/task/dto/task.dto'
import { Task } from '@modules/task/task.entity'
import { User } from '@modules/user/user.entity'
import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import {
  FindManyOptions,
  FindOneOptions,
  In,
  IsNull,
  Not,
  Repository,
} from 'typeorm'
import { LoggedUserDTO } from '../../commons/dto/logged'
import { formatFilter } from '../../utils/functions/date-string'
import { getLinkAsset, processLinkAsset } from '../../utils/functions/image'
import { pagingHandler, pagingResponse } from '../../utils/functions/pagination'
import { MSG } from '../../utils/messages/common'

@Injectable()
export class TaskService {
  constructor(
    @InjectRepository(Task)
    public taskRepo: Repository<Task>,
    @InjectRepository(User)
    public userRepo: Repository<User>,
    @InjectRepository(AssignTask)
    private assignTaskRepo: Repository<AssignTask>,
    @InjectRepository(AssignStaff)
    private assignStaffRepo: Repository<AssignStaff>,
    @InjectRepository(Defect)
    private defectRepo: Repository<Defect>,
    @InjectRepository(File)
    private fileRepo: Repository<File>,
    @InjectRepository(TaskStaff)
    private taskStaffRepo: Repository<TaskStaff>,
    private readonly s3Service: S3Service,
  ) {}

  async getRectifyTasks(
    tankId: Tank['id'],
    { pageIndex, pageSize }: GetRectifyTasksDTO,
  ): Promise<any> {
    const { take, skip } = pagingHandler(pageIndex, pageSize)

    const withStatus = [
      ASSIGN_TASK_STATUS.PREPARE,
      ASSIGN_TASK_STATUS.COMPLETED,
    ]

    const [data, total] = await this.assignTaskRepo
      .createQueryBuilder('assignTask')
      .leftJoin('assignTask.tank', 'tank')
      .where('tank.id = :tankId', { tankId })
      .andWhere('assignTask.status IN (:...status)', { status: withStatus })
      .skip(skip)
      .take(take)
      .getManyAndCount()

    return pagingResponse(data, total, pageIndex, pageSize)
  }

  async getInspectorRemarkTasks(
    tankId: Tank['id'],
    { pageIndex, pageSize, datetime }: GetInspectorRemarkTasksDTO,
  ): Promise<any> {
    const withStatus = [ASSIGN_TASK_STATUS.CONFIRMED]
    const pDatetime = formatFilter(datetime, 'start')
    const date = new Date(pDatetime)
    const year = date.getFullYear()
    const { take, skip } = pagingHandler(pageIndex, pageSize)
    const [data, total] = await this.assignTaskRepo
      .createQueryBuilder('assignTask')
      .leftJoinAndSelect('assignTask.tank', 'tank')
      .leftJoinAndSelect('tank.location', 'location')
      .leftJoinAndSelect('tank.material', 'material')
      .leftJoinAndSelect('assignTask.parent', 'parent')
      .leftJoinAndSelect('parent.tasks', 'parentTasks')
      .leftJoinAndSelect('parentTasks.inspectorRemarkBy', 'inspectorRemarkBy')
      .where('tank.id = :tankId', { tankId })
      .andWhere('assignTask.status IN (:...status)', { status: withStatus })
      .andWhere('EXTRACT(YEAR FROM assignTask.startAt) = :year', { year })
      .skip(skip)
      .take(take)
      .getManyAndCount()

    const transformedData = data.map(({ parent: { tasks, ...restParent } }) => {
      const inspectorRemarkMap = new Map()

      tasks.forEach((task) => {
        const { inspectorRemarkBy } = task
        if (inspectorRemarkBy) {
          const existing = inspectorRemarkMap.get(inspectorRemarkBy.id)
          const transformedObject = {
            id: inspectorRemarkBy.id,
            fullName: inspectorRemarkBy.fullName,
            firstName: inspectorRemarkBy.firstName,
            lastName: inspectorRemarkBy.lastName,
          }
          if (existing) {
            Object.assign(existing, transformedObject)
          } else {
            inspectorRemarkMap.set(inspectorRemarkBy.id, {
              ...transformedObject,
            })
          }
        }
      })

      const inspectorRemark = Array.from(inspectorRemarkMap.values())

      return {
        ...restParent,
        inspectorRemark,
      }
    })

    return pagingResponse(transformedData, total, pageIndex, pageSize)
  }

  async getStaffTasks(
    { user: { id: userId } }: LoggedUserDTO,
    { pageIndex, pageSize, startTime, endTime }: GetStaffTasksDTO,
  ): Promise<any> {
    const user = await this.userRepo.findOne({
      where: { id: userId },
      relations: { staff: true },
    })
    const { take, skip } = pagingHandler(pageIndex, pageSize)
    const staffId = user.staff.id

    const pStartTime = new Date(startTime.replace(' ', '+')).toISOString()
    const pEndTime = new Date(endTime.replace(' ', '+')).toISOString()

    const [data, total] = await this.assignTaskRepo
      .createQueryBuilder('assignTask')
      .leftJoinAndSelect('assignTask.tank', 'tank')
      .leftJoinAndSelect('tank.location', 'location')
      .leftJoinAndSelect('tank.material', 'material')
      .leftJoinAndSelect('assignTask.staffs', 'staffs')
      .leftJoinAndSelect('staffs.staff', 'staff')
      .where('staff.id IN (:...staffIds)', { staffIds: staffId.split(',') })
      .andWhere('tank.id IS NOT NULL')
      .andWhere('(assignTask.startAt BETWEEN :startTime AND :endTime)', {
        startTime: pStartTime,
        endTime: pEndTime,
      })
      .addSelect(
        `CASE 
                WHEN assignTask.status = 'prepare' THEN 1 
                WHEN assignTask.status = 'unresolved' THEN 1 
                WHEN assignTask.status = 'pre-inspection' AND assignTask.isStaffSubmitted = FALSE THEN 1 
                WHEN assignTask.status = 'pre-inspection' AND assignTask.isStaffSubmitted = TRUE THEN 2
                WHEN assignTask.status = 'completed' THEN 3 
                ELSE 4 
             END`,
        'orderstatus',
      )
      .orderBy({
        orderstatus: 'ASC',
        'assignTask.startAt': 'DESC',
      })
      .skip(skip)
      .take(take)
      .getManyAndCount()

    const transformedData = data.map(({ staffs, ...item }) => {
      return {
        ...item,
        staffs: staffs.map((ft) => {
          return {
            ...ft.staff,
          }
        }),
      }
    })

    return pagingResponse(transformedData, total, pageIndex, pageSize)
  }

  async getStaffWorkingTask(
    { user: { id: userId } }: LoggedUserDTO,
    assignTaskId: AssignTask['id'],
  ): Promise<any> {
    const user = await this.userRepo.findOne({
      where: { id: userId },
      relations: { staff: true },
    })
    const staffId = user.staff.id
    const queryBuilder = this.assignTaskRepo
      .createQueryBuilder('assignTask')
      .leftJoinAndSelect('assignTask.tank', 'tank')
      .leftJoinAndSelect('assignTask.parent', 'parent')
      .leftJoinAndSelect('parent.tasks', 'parentTasks')
      .leftJoinAndSelect('parentTasks.defect', 'parentTaskDefect')
      .leftJoinAndSelect('parentTasks.images', 'parentTaskImages')
      .leftJoinAndSelect('tank.location', 'location')
      .leftJoinAndSelect('tank.material', 'material')
      .leftJoinAndSelect('assignTask.tasks', 'tasks')
      .leftJoinAndSelect('tasks.defect', 'defect')
      .leftJoinAndSelect('tasks.images', 'images')
      .leftJoinAndSelect('tasks.staffs', 'staffs')
      .leftJoinAndSelect('staffs.staff', 'staff')
      .leftJoinAndSelect('assignTask.staffs', 'assignTaskStaffs')
      .leftJoinAndSelect('assignTaskStaffs.staff', 'assignTaskStaff')
      .where('assignTask.id = :assignTaskId', { assignTaskId })
      .andWhere('staff.id IN (:...staffIds)', { staffIds: staffId.split(',') })

    const dtAssignTask = await queryBuilder.getOne()

    let transformedData = null
    if (dtAssignTask) {
      const { parent, ...restAssignTask } = dtAssignTask

      transformedData = {
        ...restAssignTask,
        staffs: dtAssignTask.staffs.map((ft) => ({ ...ft.staff })),
        tasks: dtAssignTask.tasks
          .map(({ defect, ...item }) => {
            let dtBeforeDefect
            if (parent) {
              for (const parentTask of parent.tasks) {
                if (parentTask.defect.id === defect.id) {
                  dtBeforeDefect = parentTask
                  break
                }
              }
            }
            if (dtBeforeDefect) {
              processLinkAsset(dtBeforeDefect.images, FILE_TYPE.IMAGE)
            }
            processLinkAsset(item.images, FILE_TYPE.IMAGE)
            return {
              ...item,
              title: defect.title,
              type: defect.type,
              before: dtBeforeDefect
                ? {
                    images: dtBeforeDefect.images,
                    staffRemark: dtBeforeDefect.staffRemark,
                  }
                : null,
            }
          })
          .sort((a, b) => a.title.localeCompare(b.title))
          .sort((a, b) => Number(a.extra) - Number(b.extra)),
      }
    }

    return transformedData
  }

  async getInspectorWorkingTask(assignTaskId: AssignTask['id']): Promise<any> {
    const queryBuilder = this.assignTaskRepo
      .createQueryBuilder('assignTask')
      .leftJoinAndSelect('assignTask.tank', 'tank')
      .leftJoinAndSelect('tank.location', 'location')
      .leftJoinAndSelect('tank.material', 'material')
      .leftJoinAndSelect('assignTask.tasks', 'tasks')
      .leftJoinAndSelect('tasks.defect', 'defect')
      .leftJoinAndSelect('tasks.images', 'images')
      .leftJoinAndSelect('tasks.staffs', 'staffs')
      .leftJoinAndSelect('tasks.inspectorRemarkImages', 'inspectorRemarkImages')
      .leftJoinAndSelect('staffs.staff', 'staff')
      .leftJoinAndSelect('assignTask.staffs', 'assignTaskStaffs')
      .leftJoinAndSelect('assignTaskStaffs.staff', 'assignTaskStaff')
      .leftJoinAndSelect(
        'assignTask.inspectorSubmittedBy',
        'inspectorSubmittedBy',
      )
      .where('assignTask.id = :assignTaskId', { assignTaskId })

    const dtAssignTask = await queryBuilder.getOne()

    let transformedData = null
    if (dtAssignTask) {
      const {
        staffs,
        customerSignature,
        customerSignatureDate,
        inspectorSubmittedBy,
        ...restAssignTask
      } = dtAssignTask

      transformedData = {
        ...restAssignTask,
        remarkBy: inspectorSubmittedBy
          ? {
              id: inspectorSubmittedBy.id,
              fullName: inspectorSubmittedBy.fullName,
              firstName: inspectorSubmittedBy.firstName,
              lastName: inspectorSubmittedBy.lastName,
            }
          : null,
        tasks: dtAssignTask.tasks
          .map(({ staffs, defect, ...item }) => {
            processLinkAsset(item.images, FILE_TYPE.IMAGE)
            processLinkAsset(item.inspectorRemarkImages, FILE_TYPE.IMAGE)
            return {
              ...item,
              title: defect.title,
              type: defect.type,
            }
          })
          .sort((a, b) => a.title.localeCompare(b.title)),
      }
    }

    return transformedData
  }

  async createNewDefect(
    payload: CreateNewDefectDTO,
    { user }: LoggedUserDTO,
  ): Promise<any> {
    const { assignTaskId, imageIds, ...rest } = payload

    const findAssignTask = await this.assignTaskRepo.findOne({
      where: {
        id: assignTaskId,
      },
    })
    if (!findAssignTask) {
      throw new NotFoundException(MSG.TASK_NOT_FOUND)
    }

    const dtDefect = await this.defectRepo.save({
      ...rest,
    })

    const dtTask = await this.taskRepo.save({
      ...rest,
      defect: {
        id: dtDefect.id,
      },
      assignTask: {
        id: assignTaskId,
      },
      extra: !!(payload.qty || payload.size),
    })

    if (user?.staff) {
      await this.taskStaffRepo.save({
        task: {
          id: dtTask.id,
        },
        staff: {
          id: user?.staff.id,
        },
      })
    }

    if (imageIds && imageIds.length) {
      for (const imageId of imageIds) {
        const findFile = await this.fileRepo.findOne({
          where: {
            id: imageId,
            type: FILE_TYPE.IMAGE,
          },
        })
        if (findFile) {
          await this.fileRepo.save({
            task: {
              id: dtTask.id,
            },
            id: imageId,
          })
        }
      }
    }

    const findTask = await this.taskRepo.findOne({
      where: {
        id: dtTask.id,
      },
      relations: ['images', 'defect'],
    })
    if (findTask.images.length) {
      processLinkAsset(findTask.images, FILE_TYPE.IMAGE)
    }
    const { defect, ...item } = findTask
    return {
      ...item,
      title: defect.title,
      type: defect.type,
    }
  }

  async updateNewDefect(
    payload: UpdateNewDefectDTO,
    { user: { id: userId } }: LoggedUserDTO,
    newTaskId: Task['id'],
  ): Promise<any> {
    const { imageIds, removeImageIds, ...rest } = payload
    const user = await this.userRepo.findOne({
      where: { id: userId },
      relations: { staff: true },
    })

    let findTask = await this.taskRepo.findOne({
      where: {
        id: newTaskId,
        staffs: {
          staff: {
            id: user.staff.id,
          },
        },
        defect: {
          type: IsNull(),
        },
      },
      relations: ['defect'],
    } as FindOneOptions<Task>)
    if (!findTask) {
      throw new NotFoundException(MSG.TASK_NOT_FOUND)
    }

    await this.defectRepo.save({
      ...rest,
      id: findTask.defect.id,
    })

    await this.taskRepo.save({
      ...rest,
      id: newTaskId,
      extra: !!(payload.qty || payload.size),
    })

    if (imageIds && imageIds.length) {
      for (const imageId of imageIds) {
        const findFile = await this.fileRepo.findOne({
          where: {
            id: imageId,
            type: FILE_TYPE.IMAGE,
          },
        })
        if (findFile) {
          await this.fileRepo.save({
            task: {
              id: newTaskId,
            },
            id: imageId,
          })
        }
      }
    }

    if (removeImageIds && removeImageIds.length) {
      for (const imageId of removeImageIds) {
        await this.fileRepo.update(imageId, {
          isDeleted: true,
          deletedAt: new Date(),
        })
      }
    }

    findTask = await this.taskRepo.findOne({
      where: {
        id: newTaskId,
      },
      relations: ['images', 'defect'],
    })
    if (findTask.images.length) {
      processLinkAsset(findTask.images, FILE_TYPE.IMAGE)
    }
    const { defect, ...item } = findTask
    return {
      ...item,
      title: defect.title,
      type: defect.type,
    }
  }

  async updateExtraDefect(
    payload: UpdateExtraDefectDTO,
    { user: { id: userId } }: LoggedUserDTO,
    newTaskId: Task['id'],
  ): Promise<any> {
    const { ...rest } = payload
    const user = await this.userRepo.findOne({
      where: { id: userId },
      relations: { staff: true },
    })
    let findTask = await this.taskRepo.findOne({
      where: {
        id: newTaskId,
        staffs: {
          staff: {
            id: user.staff.id,
          },
        },
        extra: true,
      },
    } as FindOneOptions<Task>)
    if (!findTask) {
      throw new NotFoundException(MSG.TASK_NOT_FOUND)
    }

    await this.taskRepo.save({
      ...rest,
      id: newTaskId,
    })

    findTask = await this.taskRepo.findOne({
      where: {
        id: newTaskId,
      },
      relations: ['images', 'defect'],
    })
    if (findTask.images.length) {
      processLinkAsset(findTask.images, FILE_TYPE.IMAGE)
    }
    const { defect, ...item } = findTask
    return {
      ...item,
      title: defect.title,
      type: defect.type,
    }
  }

  async requestRectifyDefect(
    payload: RequestRectifyDefectDTO,
    { user: { id: userId } }: LoggedUserDTO,
  ): Promise<any> {
    const user = await this.userRepo.findOne({
      where: { id: userId },
      relations: { staff: true },
    })

    if (payload.taskIds && payload.taskIds.length > 0) {
      const tasks = await this.taskRepo.find({
        where: {
          id: In(payload.taskIds),
          assignTask: {
            id: payload.assignTaskId,
          },
          staffs: {
            staff: {
              id: user.staff.id,
            },
          },
        },
      } as FindManyOptions<Task>)

      if (tasks && !tasks.length) {
        throw new NotFoundException(MSG.TASK_NOT_FOUND)
      }

      for (const task of tasks) {
        await this.taskRepo.save({
          id: task.id,
          status: TASK_STATUS.RECTIFY,
        })
      }
    }

    await this.assignTaskRepo.save({
      id: payload.assignTaskId,
      isStaffSubmitted: true,
      staffSubmittedBy: {
        id: user.staff.id,
      },
    })

    return true
  }

  async inspectorSubmitDefect(
    payload: InspectorSubmitDefectDTO,
    { user }: LoggedUserDTO,
  ): Promise<any> {
    let findAssignTask = await this.assignTaskRepo.findOne({
      where: {
        id: payload.assignTaskId,
        status: Not(In([ASSIGN_TASK_STATUS.COMPLETED])),
      },
    })
    if (findAssignTask) {
      throw new BadRequestException(MSG.TASK_HAS_NOT_BEEN_COMPLETED)
    }

    findAssignTask = await this.assignTaskRepo.findOne({
      where: {
        id: payload.assignTaskId,
        isInspectorSubmitted: false,
      },
    })
    if (!findAssignTask) {
      throw new BadRequestException(MSG.TASK_HAS_BEEN_SUBMITTED)
    }

    await this.assignTaskRepo.save({
      id: payload.assignTaskId,
      isInspectorSubmitted: true,
      inspectorSubmittedBy: {
        id: user.id,
      },
      inspectorSubmittedDate: new Date(),
    })

    return true
  }

  async staffAddRemark(
    payload: StaffAddRemarkDTO,
    { user: { id: userId } }: LoggedUserDTO,
  ): Promise<any> {
    const user = await this.userRepo.findOne({
      where: { id: userId },
      relations: { staff: true },
    })
    let findTask = await this.taskRepo.findOne({
      where: {
        id: payload.taskId,
        status: In([TASK_STATUS.UN_COMPLETED, TASK_STATUS.COMPLETED]),
      },
    } as FindOneOptions<Task>)
    if (findTask) {
      throw new BadRequestException(MSG.TASK_HAS_BEEN_COMPLETED)
    }

    findTask = await this.taskRepo.findOne({
      where: {
        id: payload.taskId,
        staffs: {
          staff: {
            id: user.staff.id,
          },
        },
      },
    } as FindOneOptions<Task>)
    if (!findTask) {
      throw new NotFoundException(MSG.TASK_NOT_FOUND)
    }

    await this.taskRepo.save({
      id: findTask.id,
      staffRemark: payload.remark,
      staffRemarkBy: {
        id: user.staff.id,
      },
    })

    findTask = await this.taskRepo.findOne({
      where: {
        id: payload.taskId,
      },
      relations: [
        'images',
        'defect',
        'assignTask',
        'assignTask.parent',
        'assignTask.parent.tasks',
        'assignTask.parent.tasks.defect',
        'assignTask.parent.tasks.images',
      ],
    })
    if (findTask.images.length) {
      processLinkAsset(findTask.images, FILE_TYPE.IMAGE)
    }
    let dtBeforeDefect
    if (findTask.assignTask.parent) {
      for (const parentTask of findTask.assignTask.parent.tasks) {
        if (parentTask.defect.id === findTask.defect.id) {
          dtBeforeDefect = parentTask
          break
        }
      }
    }
    if (dtBeforeDefect) {
      processLinkAsset(dtBeforeDefect.images, FILE_TYPE.IMAGE)
    }
    const { defect, assignTask, ...item } = findTask
    return {
      ...item,
      title: defect.title,
      type: defect.type,
      before: dtBeforeDefect
        ? {
            images: dtBeforeDefect.images,
            staffRemark: dtBeforeDefect.staffRemark,
          }
        : null,
    }
  }

  async inspectorAddRemark(
    payload: InspectorAddRemarkDTO,
    { user }: LoggedUserDTO,
  ): Promise<any> {
    let findTask = await this.taskRepo.findOne({
      where: {
        id: payload.taskId,
        assignTask: {
          isInspectorSubmitted: true,
        },
      },
    } as FindOneOptions<Task>)
    if (findTask) {
      throw new BadRequestException(MSG.TASK_HAS_BEEN_SUBMITTED)
    }

    findTask = await this.taskRepo.findOne({
      where: {
        id: payload.taskId,
      },
    } as FindOneOptions<Task>)
    if (!findTask) {
      throw new NotFoundException(MSG.TASK_NOT_FOUND)
    }

    await this.taskRepo.save({
      id: findTask.id,
      inspectorRemark: payload.remark,
      inspectorRemarkBy: {
        id: user.id,
      },
    })

    findTask = await this.taskRepo.findOne({
      where: {
        id: payload.taskId,
      },
      relations: ['images', 'defect'],
    })
    if (findTask.images.length) {
      processLinkAsset(findTask.images, FILE_TYPE.IMAGE)
    }
    const { defect, assignTask, ...item } = findTask
    return {
      ...item,
      title: defect.title,
      type: defect.type,
    }
  }

  async inspectorAddRemarkImages(
    payload: InspectorAddRemarkImagesDTO,
    { user }: LoggedUserDTO,
  ): Promise<any> {
    let findTask = await this.taskRepo.findOne({
      where: {
        id: payload.taskId,
        assignTask: {
          isInspectorSubmitted: true,
        },
      },
    } as FindOneOptions<Task>)
    if (findTask) {
      throw new BadRequestException(MSG.TASK_HAS_BEEN_SUBMITTED)
    }

    findTask = await this.taskRepo.findOne({
      where: {
        id: payload.taskId,
      },
    } as FindOneOptions<Task>)
    if (!findTask) {
      throw new NotFoundException(MSG.TASK_NOT_FOUND)
    }

    const files = await this.fileRepo.find({
      where: { id: In(payload.imageIds) },
    })

    await this.taskRepo.save({
      id: findTask.id,
      inspectorRemarkImages: files,
      inspectorRemarkBy: {
        id: user.id,
      },
    })

    findTask = await this.taskRepo.findOne({
      where: {
        id: payload.taskId,
      },
      relations: ['images', 'defect', 'inspectorRemarkImages'],
    })
    if (findTask.images.length) {
      processLinkAsset(findTask.images, FILE_TYPE.IMAGE)
    }
    if (findTask.inspectorRemarkImages.length) {
      processLinkAsset(findTask.inspectorRemarkImages, FILE_TYPE.IMAGE)
    }
    const { defect, assignTask, ...item } = findTask
    return {
      ...item,
      title: defect.title,
      type: defect.type,
    }
  }

  async deleteNewDefect(
    { user: { id: userId } }: LoggedUserDTO,
    newTaskId: Task['id'],
  ): Promise<any> {
    const user = await this.userRepo.findOne({
      where: { id: userId },
      relations: { staff: true },
    })
    const findTask = await this.taskRepo.findOne({
      where: {
        id: newTaskId,
        staffs: {
          staff: {
            id: user.staff.id,
          },
        },
        defect: {
          type: IsNull(),
        },
      },
      relations: ['defect'],
    } as FindOneOptions<Task>)
    if (!findTask) {
      throw new NotFoundException(MSG.TASK_NOT_FOUND)
    }

    const findTaskStaff = await this.taskStaffRepo.findOne({
      where: {
        task: {
          id: newTaskId,
        },
        staff: {
          id: user.staff.id,
        },
      },
    } as FindOneOptions<TaskStaff>)

    if (findTaskStaff) {
      await this.taskStaffRepo.update(findTaskStaff.id, {
        isDeleted: true,
        deletedAt: new Date(),
      })
    }

    await this.defectRepo.update(findTask.defect.id, {
      isDeleted: true,
      deletedAt: new Date(),
    })

    await this.taskRepo.update(newTaskId, {
      isDeleted: true,
      deletedAt: new Date(),
    })
  }

  // Phase 1 - Assign task
  async updateTaskAfterAssign(
    assignTaskId: AssignTask['id'],
    staffIds: string[],
  ): Promise<boolean> {
    const tasks = await this.taskRepo.find({
      where: { assignTask: { id: assignTaskId } },
    })

    const promise = []
    for (const task of tasks) {
      promise.push(this.addStaffsToTask(staffIds, task?.id))
    }
    await Promise.all(promise)

    return true
  }

  // Phase 2 - Assign staff
  async createTaskAfterAssignStaff(
    assignTaskIdPhase1: AssignTask['id'],
    assignTaskIdPhase2: AssignTask['id'],
    defects: AssignStaffDefectDTO[],
  ): Promise<boolean> {
    const defectIds = defects.map((item) => item?.defectId)
    const tasks = await this.taskRepo.find({
      where: {
        assignTask: { id: assignTaskIdPhase1 },
        defect: { id: In(defectIds) },
        status: TASK_STATUS.RECTIFY,
      },
      relations: ['defect', 'inspectorRemarkBy', 'staffRemarkBy'],
    } as FindManyOptions<Task>)
    const taskDefectIds = tasks.map((item) => item?.defect?.id)

    for (const defect of defects) {
      if (!taskDefectIds.includes(defect?.defectId)) continue
      const task = tasks.find((item) => item?.defect?.id === defect?.defectId)
      const dtTask = await this.taskRepo.save({
        defect: { id: defect?.defectId },
        assignTask: { id: assignTaskIdPhase2 },
        status: TASK_STATUS.RECTIFY,
        qty: task?.qty,
        size: task?.size,
        extra: task?.extra,
      })
      const promise = []
      promise.push(this.addStaffsToTask(defect?.staffs, dtTask?.id))
    }

    return true
  }

  // Phase 2 - Update assign staff
  async updateTaskAfterAssignStaff(
    assignTaskId: AssignTask['id'],
    defects: AssignStaffDefectDTO[],
  ): Promise<boolean> {
    const defectIds = defects.map((item) => item?.defectId)
    const tasks = await this.taskRepo.find({
      where: {
        assignTask: { id: assignTaskId },
        defect: { id: In(defectIds) },
        status: In([TASK_STATUS.RECTIFY, TASK_STATUS.UN_COMPLETED]),
      },
      relations: ['defect'],
    } as FindManyOptions<Task>)
    const taskDefectIds = tasks.map((item) => item?.defect?.id)

    for (const defect of defects) {
      if (!taskDefectIds.includes(defect?.defectId)) continue
      const task = tasks.find((item) => item?.defect?.id === defect?.defectId)
      if (task) {
        const promise = []
        promise.push(this.addStaffsToTask(defect?.staffs, task?.id))
      }
    }

    return true
  }

  async addStaffsToTask(
    staffIds: string[],
    taskId: Task['id'],
  ): Promise<boolean> {
    const taskStaff = await this.taskStaffRepo.find({
      where: { task: { id: taskId } },
      relations: ['staff'],
    } as FindManyOptions<TaskStaff>)
    const taskStaffIds = taskStaff.map((item) => item?.staff?.id)

    const promise = []
    // Add staff
    const addStaffIds = []
    for (const staffId of staffIds) {
      if (!taskStaffIds.includes(staffId)) {
        addStaffIds.push(staffId)
      }
    }

    for (const staffId of addStaffIds) {
      promise.push(
        this.taskStaffRepo.save({
          staff: { id: staffId },
          task: { id: taskId },
        }),
      )
    }

    // Remove staff
    const removeStaffIds = []
    for (const staffId of taskStaffIds) {
      if (!staffIds.includes(staffId)) {
        removeStaffIds.push(staffId)
      }
    }
    for (const staffId of removeStaffIds) {
      promise.push(this.deleteStaffByIdAndTaskId(staffId, taskId))
    }

    await Promise.all(promise)
    return true
  }

  async deleteStaffByIdAndTaskId(
    staffId: AssignStaff['id'],
    taskId: AssignTask['id'],
  ) {
    const taskStaff = await this.taskStaffRepo.findOne({
      where: { staff: { id: staffId }, task: { id: taskId } },
    })
    if (!taskStaff) {
      throw new NotFoundException(MSG.TASK_NOT_FOUND)
    }

    return this.taskStaffRepo.update(taskStaff?.id, {
      isDeleted: true,
      deletedAt: new Date(),
    })
  }

  async getTaskByDefectIds(
    assignTaskId: AssignTask['id'],
    defectIds: string[],
  ): Promise<Task[]> {
    return this.taskRepo.find({
      where: {
        assignTask: { id: assignTaskId },
        defect: { id: In(defectIds) },
        status: In([TASK_STATUS.UN_COMPLETED, TASK_STATUS.RECTIFY]),
      },
    } as FindOneOptions<Task>)
  }

  async staffReceiveSignature(
    signature: Express.Multer.File,
    { user: { id: userId } }: LoggedUserDTO,
    payload: StaffReceiveSignatureDTO,
  ): Promise<any> {
    const user = await this.userRepo.findOne({
      where: { id: userId },
      relations: { staff: true },
    })
    const findAssignTask = await this.assignTaskRepo.findOne({
      where: {
        id: payload.assignTaskId,
      },
    })
    if (!findAssignTask) {
      throw new NotFoundException(MSG.TASK_NOT_FOUND)
    }
    let dtUpload
    if (signature) {
      const prefix = `staff/${user.staff.id}/signature`
      dtUpload = await this.s3Service.processImageUpload(prefix, signature)
      if (!dtUpload) {
        throw new BadRequestException(MSG.COULD_NOT_UPLOAD)
      }
    }

    if (payload.completeIds && payload.completeIds.length) {
      const tasks = await this.taskRepo.find({
        where: {
          id: In(payload.completeIds),
          assignTask: {
            id: payload.assignTaskId,
          },
        },
      } as FindManyOptions<Task>)
      if (tasks.length) {
        for (const task of tasks) {
          await this.taskRepo.save({
            id: task.id,
            status: TASK_STATUS.COMPLETED,
            completedDate: new Date(),
            customerSignature: dtUpload ? dtUpload.link : null,
            customerSignatureDate: dtUpload ? new Date() : null,
            receiveSignatureBy: {
              id: user.staff.id,
            },
            isStaffSubmitted: true,
          })
        }
      }
    }

    const trackingTasks = await this.taskRepo.find({
      where: {
        assignTask: {
          id: payload.assignTaskId,
        },
        status: Not(TASK_STATUS.COMPLETED),
      },
    } as FindManyOptions<Task>)
    let signedUrl
    if (!trackingTasks.length) {
      await this.assignTaskRepo.save({
        id: payload.assignTaskId,
        status: ASSIGN_TASK_STATUS.COMPLETED,
        completedDate: new Date(),
        customerSignature: dtUpload ? dtUpload.link : null,
        customerSignatureDate: dtUpload ? new Date() : null,
        receiveSignatureBy: {
          id: user.staff.id,
        },
      })
      if (dtUpload) {
        signedUrl = getLinkAsset(dtUpload.link)
      }
    } else {
      await this.assignTaskRepo.save({
        id: payload.assignTaskId,
        status: ASSIGN_TASK_STATUS.UNRESOLVED,
      })
    }
    return signedUrl
  }

  async getTaskCalendarFormat(
    { user }: LoggedUserDTO,
    { startTime, endTime }: GetTaskCalendarFormatDTO,
  ): Promise<any> {
    const userId = user.id
    const withStatus = [
      ASSIGN_TASK_STATUS.PRE_INSPECTION,
      ASSIGN_TASK_STATUS.PREPARE,
      ASSIGN_TASK_STATUS.UNRESOLVED,
      ASSIGN_TASK_STATUS.COMPLETED,
    ]
    const pStartTime = new Date(startTime.replace(' ', '+')).toISOString()
    const pEndTime = new Date(endTime.replace(' ', '+')).toISOString()

    const queryBuilder = this.assignTaskRepo
      .createQueryBuilder('assignTask')
      .leftJoinAndSelect('assignTask.tank', 'tank')
      .leftJoinAndSelect('tank.location', 'location')
      .leftJoinAndSelect('location.customer', 'customer')
      .leftJoin('customer.managedBy', 'managedBy')
      .leftJoinAndSelect('assignTask.staffs', 'staffs')
      .leftJoinAndSelect('staffs.staff', 'staff')
      .where('managedBy.id IN (:...userIds)', { userIds: userId.split(',') })
      .andWhere('assignTask.status IN (:...status)', { status: withStatus })
      .andWhere('(assignTask.startAt BETWEEN :startTime AND :endTime)', {
        startTime: pStartTime,
        endTime: pEndTime,
      })
      .orderBy('assignTask.startAt', 'ASC')

    const dtAssignTask = await queryBuilder.getMany()

    return dtAssignTask.map(({ staffs, ...item }) => {
      return {
        ...item,
        staffs: staffs.map((ft) => {
          return {
            ...ft.staff,
          }
        }),
      }
    })
  }
}
