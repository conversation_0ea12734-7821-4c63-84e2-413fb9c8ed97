import {
  IsNotEmpty,
  <PERSON><PERSON>ptional,
  IsString, IsUUID,
} from 'class-validator'
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { PaginatedQueryParams } from '../../../commons/dto/query'

export class CreateLocationDTO {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  postalCode: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  blockNo: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  street: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  building?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  lat?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  long?: string

  @ApiProperty()
  @IsUUID('4')
  @IsNotEmpty()
  @IsString()
  customerId: string
}

export class UpdateLocationDTO {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  postalCode: string

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  blockNo: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  street: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  building?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  lat?: string

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  long?: string
}

export class GetLocationsDTO extends PaginatedQueryParams {}
